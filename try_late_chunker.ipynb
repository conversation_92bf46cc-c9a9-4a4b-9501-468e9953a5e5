{"cells": [{"cell_type": "code", "execution_count": 1, "id": "95b1e4c7", "metadata": {}, "outputs": [], "source": ["from chonkie import RecursiveChunker, RecursiveRules\n", "from chonkie import LateChunker\n", "\n", "chunker = LateChunker(\n", "    embedding_model=\"all-MiniLM-L6-v2\",\n", "    chunk_size=2048,\n", "    rules=RecursiveRules(),\n", "    min_characters_per_chunk=24,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "ccd46f1d", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'OutStream' object has no attribute 'reconfigure'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m RecursiveChunker\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m \u001b[43msys\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstdout\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreconfigure\u001b[49m(encoding=\u001b[33m'\u001b[39m\u001b[33mutf-8\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# Initialize the recursive chunker to chunk Chinese texts\u001b[39;00m\n\u001b[32m      6\u001b[39m chunker = RecursiveChunker.from_recipe(lang=\u001b[33m\"\u001b[39m\u001b[33mzh\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: 'OutStream' object has no attribute 'reconfigure'"]}], "source": ["from chonkie import RecursiveChunker\n", "import sys\n", "sys.stdout.reconfigure(encoding='utf-8')\n", "\n", "# Initialize the recursive chunker to chunk Chinese texts\n", "chunker = RecursiveChunker.from_recipe(lang=\"zh\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b8ac3d2", "metadata": {}, "outputs": [], "source": ["# 获取文本\n", "file_path = r\"C:\\Users\\<USER>\\Desktop\\ai_mindmap\\documents\\python第八章.md\"\n", "\n", "with open(file_path, 'r', encoding='utf-8') as file:\n", "        text = file.read()\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "0bb6bd0c", "metadata": {}, "outputs": [], "source": ["chunks = chunker.chunk(text)\n", "\n", "for chunk in chunks:\n", "    print(f\"Chunk text: {chunk.text}\")\n", "    print(f\"Token count: {chunk.token_count}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}